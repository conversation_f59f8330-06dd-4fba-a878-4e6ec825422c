{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [



    
    
    {
        "address": "TCP/IP address of process to be debugged",
        "localRoot": "${workspaceFolder}",
        "name": "Attach to Remote",
        "port": 9229,
        "remoteRoot": "Absolute path to the remote directory containing the program",
        "request": "attach",
        "skipFiles": [
            "<node_internals>/**"
        ],
        "type": "node"
    },
        {
            "name": "Python Debugger: Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal"
        },
        {
            "name": "sh_file_debug",
            "type": "debugpy",
            "request": "attach",
            "justMyCode": false, 
            "subProcess": true,
            "pathMappings": [
                {
                    "localRoot": "${workspaceFolder}",
                    "remoteRoot": "${workspaceFolder}"
                }
            ],
            "connect": {
                "host": "localhost",
                "port": 9501
            }
        }
    ]
}